'use client';

import { Canvas } from '@react-three/fiber';
import { Float, Sphere, Torus } from '@react-three/drei';
import { useRef, useMemo, memo } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

const FlowingStream = memo(({ position }: { position: [number, number, number] }) => {
  const tubeRef = useRef<THREE.Mesh>(null);

  const curve = useMemo(() => {
    const points = [];
    for (let i = 0; i <= 20; i++) {
      const t = i / 20;
      points.push(new THREE.Vector3(
        Math.sin(t * Math.PI * 2) * 2,
        (t - 0.5) * 4,
        Math.cos(t * Math.PI * 2) * 2
      ));
    }
    return new THREE.CatmullRomCurve3(points);
  }, []);

  useFrame((state) => {
    if (tubeRef.current) {
      tubeRef.current.rotation.y = state.clock.elapsedTime * 0.1;
    }
  });

  return (
    <Float speed={0.5} rotationIntensity={0.2} floatIntensity={0.5}>
      <mesh ref={tubeRef} position={position}>
        <tubeGeometry args={[curve, 64, 0.02, 8, false]} />
        <meshBasicMaterial
          color="#60a5fa"
          transparent
          opacity={0.6}
        />
      </mesh>
    </Float>
  );
});

FlowingStream.displayName = 'FlowingStream';

const ClarityOrb = memo(({ position }: { position: [number, number, number] }) => {
  const orbRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (orbRef.current && orbRef.current.material) {
      orbRef.current.rotation.x = state.clock.elapsedTime * 0.1;
      orbRef.current.rotation.y = state.clock.elapsedTime * 0.15;
      const material = orbRef.current.material as THREE.MeshStandardMaterial;
      material.opacity = 0.3 + Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  return (
    <Float speed={1} rotationIntensity={0.5} floatIntensity={1}>
      <Sphere ref={orbRef} position={position} args={[0.5, 32, 32]}>
        <meshStandardMaterial
          color="#a855f7"
          transparent
          opacity={0.3}
          emissive="#a855f7"
          emissiveIntensity={0.1}
        />
      </Sphere>
    </Float>
  );
});

ClarityOrb.displayName = 'ClarityOrb';

const StreamParticles = memo(() => {
  const particlesRef = useRef<THREE.Points>(null);
  const particleCount = 150;

  const positions = useMemo(() => {
    const pos = new Float32Array(particleCount * 3);
    for (let i = 0; i < particleCount * 3; i += 3) {
      pos[i] = (Math.random() - 0.5) * 25;     // x
      pos[i + 1] = (Math.random() - 0.5) * 20; // y
      pos[i + 2] = (Math.random() - 0.5) * 25; // z
    }
    return pos;
  }, [particleCount]);

  useFrame((state) => {
    if (particlesRef.current) {
      // Only rotate the entire particle system, don't modify individual positions
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.01;
      particlesRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.05;
    }
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        color="#60a5fa"
        size={0.02}
        transparent
        opacity={0.6}
        sizeAttenuation
      />
    </points>
  );
});

StreamParticles.displayName = 'StreamParticles';

const ClarityRings = memo(() => {
  const ring1Ref = useRef<THREE.Mesh>(null);
  const ring2Ref = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (ring1Ref.current) {
      ring1Ref.current.rotation.x = state.clock.elapsedTime * 0.05;
      ring1Ref.current.rotation.y = state.clock.elapsedTime * 0.03;
    }
    if (ring2Ref.current) {
      ring2Ref.current.rotation.y = state.clock.elapsedTime * 0.04;
      ring2Ref.current.rotation.z = state.clock.elapsedTime * 0.02;
    }
  });

  return (
    <>
      <mesh ref={ring1Ref} position={[0, 0, -12]}>
        <torusGeometry args={[6, 0.03, 8, 100]} />
        <meshBasicMaterial
          color="#60a5fa"
          transparent
          opacity={0.15}
          emissive="#60a5fa"
          emissiveIntensity={0.1}
        />
      </mesh>
      <mesh ref={ring2Ref} position={[0, 0, -15]}>
        <torusGeometry args={[8, 0.02, 8, 100]} />
        <meshBasicMaterial
          color="#a855f7"
          transparent
          opacity={0.1}
          emissive="#a855f7"
          emissiveIntensity={0.05}
        />
      </mesh>
    </>
  );
});

ClarityRings.displayName = 'ClarityRings';

const Scene = memo(() => {
  return (
    <>
      {/* Lighting setup for clarity theme */}
      <ambientLight intensity={0.2} />
      <directionalLight position={[10, 10, 5]} intensity={0.3} />
      <pointLight position={[-10, 5, -5]} intensity={0.2} color="#60a5fa" />
      <pointLight position={[10, -5, -5]} intensity={0.15} color="#a855f7" />

      {/* Background rings */}
      <ClarityRings />

      {/* Flowing streams */}
      <FlowingStream position={[-3, 0, -5]} />
      <FlowingStream position={[3, 0, -7]} />
      <FlowingStream position={[0, 2, -6]} />

      {/* Clarity orbs */}
      <ClarityOrb position={[-2, 3, -3]} />
      <ClarityOrb position={[4, -2, -4]} />
      <ClarityOrb position={[-1, -1, -2]} />

      {/* Stream particles */}
      <StreamParticles />
    </>
  );
});

Scene.displayName = 'Scene';

export default function ThreeBackground() {
  return (
    <div className="fixed inset-0 z-0">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        style={{ background: 'transparent' }}
        dpr={[1, 2]}
        performance={{ min: 0.5 }}
        frameloop="always"
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        <Scene />
      </Canvas>
    </div>
  );
}
