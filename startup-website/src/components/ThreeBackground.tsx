'use client';

import { Canvas } from '@react-three/fiber';
import { Float, Sphere, Box, Torus } from '@react-three/drei';
import { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

function FloatingGeometry({ position, geometry }: { position: [number, number, number], geometry: 'sphere' | 'box' | 'torus' }) {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * 0.2;
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.3;
    }
  });

  const GeometryComponent = geometry === 'sphere' ? Sphere : geometry === 'box' ? Box : Torus;

  return (
    <Float speed={1.5} rotationIntensity={1} floatIntensity={2}>
      <GeometryComponent ref={meshRef} position={position} args={geometry === 'torus' ? [0.5, 0.2, 8, 16] : [0.8]}>
        <meshStandardMaterial
          color="#ffffff"
          transparent
          opacity={0.1}
          wireframe
        />
      </GeometryComponent>
    </Float>
  );
}

function Particles() {
  const particlesRef = useRef<THREE.Points>(null);
  const particleCount = 200;

  const positions = useMemo(() => {
    const pos = new Float32Array(particleCount * 3);
    for (let i = 0; i < particleCount * 3; i++) {
      pos[i] = (Math.random() - 0.5) * 30;
    }
    return pos;
  }, [particleCount]);

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.02;
      particlesRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.1;
    }
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        color="#ffffff"
        size={0.015}
        transparent
        opacity={0.4}
        sizeAttenuation
      />
    </points>
  );
}

function MovingRings() {
  const ring1Ref = useRef<THREE.Mesh>(null);
  const ring2Ref = useRef<THREE.Mesh>(null);
  const ring3Ref = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (ring1Ref.current) {
      ring1Ref.current.rotation.x = state.clock.elapsedTime * 0.1;
      ring1Ref.current.rotation.z = state.clock.elapsedTime * 0.15;
    }
    if (ring2Ref.current) {
      ring2Ref.current.rotation.y = state.clock.elapsedTime * 0.08;
      ring2Ref.current.rotation.x = state.clock.elapsedTime * 0.12;
    }
    if (ring3Ref.current) {
      ring3Ref.current.rotation.z = state.clock.elapsedTime * 0.06;
      ring3Ref.current.rotation.y = state.clock.elapsedTime * 0.1;
    }
  });

  return (
    <>
      <mesh ref={ring1Ref} position={[0, 0, -8]}>
        <torusGeometry args={[3, 0.02, 8, 100]} />
        <meshBasicMaterial color="#ffffff" transparent opacity={0.1} />
      </mesh>
      <mesh ref={ring2Ref} position={[0, 0, -6]}>
        <torusGeometry args={[2, 0.015, 8, 100]} />
        <meshBasicMaterial color="#ffffff" transparent opacity={0.08} />
      </mesh>
      <mesh ref={ring3Ref} position={[0, 0, -10]}>
        <torusGeometry args={[4, 0.025, 8, 100]} />
        <meshBasicMaterial color="#ffffff" transparent opacity={0.06} />
      </mesh>
    </>
  );
}

function Scene() {
  return (
    <>
      {/* Ambient lighting */}
      <ambientLight intensity={0.15} />
      <directionalLight position={[10, 10, 5]} intensity={0.2} />
      <pointLight position={[-10, -10, -10]} intensity={0.1} color="#4f46e5" />

      {/* Moving rings in background */}
      <MovingRings />

      {/* Floating geometric shapes */}
      <FloatingGeometry position={[-4, 2, -2]} geometry="sphere" />
      <FloatingGeometry position={[4, -1, -3]} geometry="box" />
      <FloatingGeometry position={[0, 3, -4]} geometry="torus" />
      <FloatingGeometry position={[-2, -2, -1]} geometry="sphere" />
      <FloatingGeometry position={[3, 1, -2]} geometry="torus" />
      <FloatingGeometry position={[-1, 0, -5]} geometry="box" />

      {/* Particles */}
      <Particles />
    </>
  );
}

export default function ThreeBackground() {
  return (
    <div className="fixed inset-0 z-0">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        style={{ background: 'transparent' }}
        dpr={[1, 2]}
        performance={{ min: 0.5 }}
        frameloop="demand"
        gl={{
          antialias: false,
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        <Scene />
      </Canvas>
    </div>
  );
}
