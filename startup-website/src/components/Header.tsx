export default function Header() {
  return (
    <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white bg-opacity-5 border border-white border-opacity-10 rounded-full pt-3 pr-4 pb-3 pl-4 shadow-xl backdrop-blur-md">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="white" strokeWidth="2"></circle>
            <circle cx="12" cy="12" r="3" fill="white"></circle>
          </svg>
          <span className="ml-2 text-sm font-medium">Minimal</span>
        </div>
        <div className="hidden md:flex items-center space-x-6 text-xs text-gray-300 ml-8">
          <a href="#" className="hover:text-white transition-colors">Work</a>
          <a href="#" className="hover:text-white transition-colors">Studio</a>
          <a href="#" className="hover:text-white transition-colors">Process</a>
          <a href="#" className="hover:text-white transition-colors">Journal</a>
          <a href="#" className="hover:text-white transition-colors">Contact</a>
        </div>
        <div className="flex items-center space-x-3 ml-8">
          <a href="#" className="hidden md:inline-block text-xs font-medium hover:text-white transition-colors">Login</a>
          <a href="#" className="hover:bg-gray-200 transition-colors text-xs font-medium text-black bg-white rounded-full pt-1.5 pr-3 pb-1.5 pl-3">Start Project</a>
        </div>
      </div>
    </nav>
  );
}
