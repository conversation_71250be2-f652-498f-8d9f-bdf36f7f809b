export default function Header() {
  return (
    <nav className="container mx-auto px-6 py-6 relative z-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <svg className="h-8 w-8 text-blue-400" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"></path>
          </svg>
          <span className="ml-3 text-xl tracking-tight">Clarity</span>
        </div>
        <div className="hidden md:flex space-x-10 text-sm text-gray-300">
          <a href="#" className="hover:text-white transition-colors">Features</a>
          <a href="#" className="hover:text-white transition-colors">Solutions</a>
          <a href="#" className="hover:text-white transition-colors">Resources</a>
          <a href="#" className="hover:text-white transition-colors">Pricing</a>
        </div>
        <div>
          <button className="text-sm border border-gray-700 rounded-md px-4 py-2 hover:bg-white/5 transition-all">
            Sign in
          </button>
        </div>
      </div>
    </nav>
  );
}
