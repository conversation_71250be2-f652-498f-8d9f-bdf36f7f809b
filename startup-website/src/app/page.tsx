export default function Home() {
  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Spline 3D background */}
      <div className="fixed inset-0 z-0">
        <iframe
          src="https://my.spline.design/thresholddarkambientui-v0gkZCfi6zXm69kE0wccy70f/"
          frameBorder="0"
          width="100%"
          height="100%"
          className="w-full h-full"
        />
      </div>

      {/* Hero content */}
      <div className="relative z-10 flex flex-col items-center justify-center px-6 pt-32 pb-32 md:pt-40 md:pb-40 text-center min-h-screen">
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] -translate-x-1/2 -translate-y-1/2 bg-white opacity-5 blur-[100px] rounded-full pointer-events-none"></div>

        <span className="px-3 py-1 text-xs font-medium text-white bg-white bg-opacity-10 backdrop-blur-sm rounded-full mb-8 border border-white border-opacity-20">
          Design Studio
        </span>

        <h1 className="md:text-6xl max-w-4xl leading-tight text-4xl font-medium tracking-tighter">
          Crafting digital experiences through minimalist design
        </h1>

        <p className="md:text-xl max-w-2xl text-lg text-neutral-300 mt-6">
          We believe in the power of simplicity. Clean lines, purposeful spaces,
          and thoughtful interactions that speak volumes.
        </p>

        <div className="mt-12 flex flex-col sm:flex-row gap-4">
          <a href="#" className="px-8 py-3 bg-white text-black font-medium rounded-full hover:bg-gray-200 transition-all duration-300 shadow-lg hover:shadow-xl">
            View Our Work
          </a>
          <a href="#" className="px-8 py-3 bg-white bg-opacity-10 backdrop-blur-sm text-white font-medium rounded-full hover:bg-opacity-20 transition-all duration-300 border border-white border-opacity-20">
            Let&apos;s Talk
          </a>
        </div>

        <div className="mt-20 flex justify-center">
          <div className="w-[768px] h-[400px] bg-black bg-opacity-40 backdrop-blur-md rounded-lg shadow-2xl border border-white border-opacity-10 overflow-hidden">
            <div className="h-8 border-b border-white border-opacity-10 flex items-center px-4">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-white bg-opacity-30"></div>
                <div className="w-3 h-3 rounded-full bg-white bg-opacity-30"></div>
                <div className="w-3 h-3 rounded-full bg-white bg-opacity-30"></div>
              </div>
            </div>
            <div className="p-4 opacity-70">
              {/* App placeholder content */}
              <div className="flex space-x-4">
                <div className="w-48 h-full bg-white bg-opacity-5 backdrop-blur-sm rounded p-3 border border-white border-opacity-5">
                  <div className="w-full h-4 bg-white bg-opacity-20 rounded mb-3"></div>
                  <div className="w-3/4 h-3 bg-white bg-opacity-15 rounded mb-4"></div>
                  <div className="space-y-2">
                    <div className="w-full h-8 bg-white bg-opacity-10 rounded"></div>
                    <div className="w-full h-8 bg-white bg-opacity-10 rounded"></div>
                    <div className="w-full h-8 bg-white bg-opacity-10 rounded"></div>
                  </div>
                </div>
                <div className="flex-1 bg-white bg-opacity-5 backdrop-blur-sm rounded p-3 border border-white border-opacity-5">
                  <div className="w-1/3 h-4 bg-white bg-opacity-15 rounded mb-3"></div>
                  <div className="w-full h-[320px] bg-white bg-opacity-10 rounded flex items-center justify-center">
                    <div className="w-32 h-32 border-2 border-white border-opacity-20 rounded-full flex items-center justify-center">
                      <div className="w-16 h-16 bg-white bg-opacity-10 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
