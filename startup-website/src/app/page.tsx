import ThreeBackground from '@/components/ThreeBackground';

export default function Home() {
  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Three.js 3D background */}
      <ThreeBackground />

      {/* Content Overlay */}
      <div className="relative z-10 h-screen overflow-hidden">
        {/* Hero content */}
        <div className="container mx-auto px-6 pt-16 md:pt-24">
          <div className="flex flex-col items-center text-center max-w-3xl mx-auto">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-light tracking-tighter mb-6 leading-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400">Streamline</span> your digital experience
            </h1>
            <p className="text-gray-300 text-xl md:text-2xl mb-8 max-w-2xl font-light tracking-wide">
              Experience clarity in every interaction with our intuitive platform designed for the modern digital landscape.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 mt-4">
              <button className="bg-white text-black font-light rounded-md px-8 py-3 hover:bg-opacity-90 transition-all">
                Get started
              </button>
              <a href="#" className="flex items-center text-gray-300 hover:text-white transition-colors py-3 px-2 group">
                Learn more
                <span className="ml-1 group-hover:translate-x-1 transition-transform">→</span>
              </a>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-20 max-w-4xl mx-auto">
            <div>
              <p className="text-4xl font-light mb-1 tracking-tight">95%</p>
              <p className="text-gray-400 font-extralight">User satisfaction</p>
            </div>
            <div>
              <p className="text-4xl font-light mb-1 tracking-tight">15k+</p>
              <p className="text-gray-400 font-extralight">Active users</p>
            </div>
            <div>
              <p className="text-4xl font-light mb-1 tracking-tight">24/7</p>
              <p className="text-gray-400 font-extralight">Support available</p>
            </div>
            <div>
              <p className="text-4xl font-light mb-1 tracking-tight">100%</p>
              <p className="text-gray-400 font-extralight">Cloud-based</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
