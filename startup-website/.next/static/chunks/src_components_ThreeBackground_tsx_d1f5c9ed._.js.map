{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Medicello%20%26%20Co/Software%20Development/startup-website/src/components/ThreeBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { Canvas } from '@react-three/fiber';\nimport { Float, Sphere, Torus } from '@react-three/drei';\nimport { useRef, useMemo } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport * as THREE from 'three';\n\nfunction FlowingStream({ position }: { position: [number, number, number] }) {\n  const tubeRef = useRef<THREE.Mesh>(null);\n\n  const curve = useMemo(() => {\n    const points = [];\n    for (let i = 0; i <= 20; i++) {\n      const t = i / 20;\n      points.push(new THREE.Vector3(\n        Math.sin(t * Math.PI * 2) * 2,\n        (t - 0.5) * 4,\n        Math.cos(t * Math.PI * 2) * 2\n      ));\n    }\n    return new THREE.CatmullRomCurve3(points);\n  }, []);\n\n  useFrame((state) => {\n    if (tubeRef.current) {\n      tubeRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n    }\n  });\n\n  return (\n    <Float speed={0.5} rotationIntensity={0.2} floatIntensity={0.5}>\n      <mesh ref={tubeRef} position={position}>\n        <tubeGeometry args={[curve, 64, 0.02, 8, false]} />\n        <meshBasicMaterial\n          color=\"#60a5fa\"\n          transparent\n          opacity={0.6}\n        />\n      </mesh>\n    </Float>\n  );\n}\n\nfunction ClarityOrb({ position }: { position: [number, number, number] }) {\n  const orbRef = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (orbRef.current && orbRef.current.material) {\n      orbRef.current.rotation.x = state.clock.elapsedTime * 0.1;\n      orbRef.current.rotation.y = state.clock.elapsedTime * 0.15;\n      const material = orbRef.current.material as THREE.MeshStandardMaterial;\n      material.opacity = 0.3 + Math.sin(state.clock.elapsedTime * 2) * 0.1;\n    }\n  });\n\n  return (\n    <Float speed={1} rotationIntensity={0.5} floatIntensity={1}>\n      <Sphere ref={orbRef} position={position} args={[0.5, 32, 32]}>\n        <meshStandardMaterial\n          color=\"#a855f7\"\n          transparent\n          opacity={0.3}\n          emissive=\"#a855f7\"\n          emissiveIntensity={0.1}\n        />\n      </Sphere>\n    </Float>\n  );\n}\n\nfunction StreamParticles() {\n  const particlesRef = useRef<THREE.Points>(null);\n  const particleCount = 150;\n\n  const positions = useMemo(() => {\n    const pos = new Float32Array(particleCount * 3);\n    for (let i = 0; i < particleCount * 3; i += 3) {\n      pos[i] = (Math.random() - 0.5) * 25;     // x\n      pos[i + 1] = (Math.random() - 0.5) * 20; // y\n      pos[i + 2] = (Math.random() - 0.5) * 25; // z\n    }\n    return pos;\n  }, [particleCount]);\n\n  useFrame((state) => {\n    if (particlesRef.current) {\n      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.01;\n\n      // Animate individual particles\n      const positions = particlesRef.current.geometry.attributes.position.array as Float32Array;\n      for (let i = 1; i < positions.length; i += 3) {\n        positions[i] += Math.sin(state.clock.elapsedTime + i) * 0.001;\n      }\n      particlesRef.current.geometry.attributes.position.needsUpdate = true;\n    }\n  });\n\n  return (\n    <points ref={particlesRef}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          count={particleCount}\n          array={positions}\n          itemSize={3}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        color=\"#60a5fa\"\n        size={0.02}\n        transparent\n        opacity={0.6}\n        sizeAttenuation\n      />\n    </points>\n  );\n}\n\nfunction ClarityRings() {\n  const ring1Ref = useRef<THREE.Mesh>(null);\n  const ring2Ref = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (ring1Ref.current) {\n      ring1Ref.current.rotation.x = state.clock.elapsedTime * 0.05;\n      ring1Ref.current.rotation.y = state.clock.elapsedTime * 0.03;\n    }\n    if (ring2Ref.current) {\n      ring2Ref.current.rotation.y = state.clock.elapsedTime * 0.04;\n      ring2Ref.current.rotation.z = state.clock.elapsedTime * 0.02;\n    }\n  });\n\n  return (\n    <>\n      <mesh ref={ring1Ref} position={[0, 0, -12]}>\n        <torusGeometry args={[6, 0.03, 8, 100]} />\n        <meshBasicMaterial\n          color=\"#60a5fa\"\n          transparent\n          opacity={0.15}\n          emissive=\"#60a5fa\"\n          emissiveIntensity={0.1}\n        />\n      </mesh>\n      <mesh ref={ring2Ref} position={[0, 0, -15]}>\n        <torusGeometry args={[8, 0.02, 8, 100]} />\n        <meshBasicMaterial\n          color=\"#a855f7\"\n          transparent\n          opacity={0.1}\n          emissive=\"#a855f7\"\n          emissiveIntensity={0.05}\n        />\n      </mesh>\n    </>\n  );\n}\n\nfunction Scene() {\n  return (\n    <>\n      {/* Lighting setup for clarity theme */}\n      <ambientLight intensity={0.2} />\n      <directionalLight position={[10, 10, 5]} intensity={0.3} />\n      <pointLight position={[-10, 5, -5]} intensity={0.2} color=\"#60a5fa\" />\n      <pointLight position={[10, -5, -5]} intensity={0.15} color=\"#a855f7\" />\n\n      {/* Background rings */}\n      <ClarityRings />\n\n      {/* Flowing streams */}\n      <FlowingStream position={[-3, 0, -5]} />\n      <FlowingStream position={[3, 0, -7]} />\n      <FlowingStream position={[0, 2, -6]} />\n\n      {/* Clarity orbs */}\n      <ClarityOrb position={[-2, 3, -3]} />\n      <ClarityOrb position={[4, -2, -4]} />\n      <ClarityOrb position={[-1, -1, -2]} />\n\n      {/* Stream particles */}\n      <StreamParticles />\n    </>\n  );\n}\n\nexport default function ThreeBackground() {\n  return (\n    <div className=\"fixed inset-0 z-0\">\n      <Canvas\n        camera={{ position: [0, 0, 5], fov: 75 }}\n        style={{ background: 'transparent' }}\n        dpr={[1, 2]}\n        performance={{ min: 0.5 }}\n        frameloop=\"always\"\n        gl={{\n          antialias: true,\n          alpha: true,\n          powerPreference: \"high-performance\"\n        }}\n      >\n        <Scene />\n      </Canvas>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,SAAS,cAAc,EAAE,QAAQ,EAA0C;;IACzE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc;IAEnC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wCAAE;YACpB,MAAM,SAAS,EAAE;YACjB,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;gBAC5B,MAAM,IAAI,IAAI;gBACd,OAAO,IAAI,CAAC,IAAI,kJAAA,CAAA,UAAa,CAC3B,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,GAAG,KAAK,GAC5B,CAAC,IAAI,GAAG,IAAI,GACZ,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,GAAG,KAAK;YAEhC;YACA,OAAO,IAAI,kJAAA,CAAA,mBAAsB,CAAC;QACpC;uCAAG,EAAE;IAEL,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kCAAE,CAAC;YACR,IAAI,QAAQ,OAAO,EAAE;gBACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD;QACF;;IAEA,qBACE,6LAAC,4JAAA,CAAA,QAAK;QAAC,OAAO;QAAK,mBAAmB;QAAK,gBAAgB;kBACzD,cAAA,6LAAC;YAAK,KAAK;YAAS,UAAU;;8BAC5B,6LAAC;oBAAa,MAAM;wBAAC;wBAAO;wBAAI;wBAAM;wBAAG;qBAAM;;;;;;8BAC/C,6LAAC;oBACC,OAAM;oBACN,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAKnB;GAlCS;;QAgBP,kNAAA,CAAA,WAAQ;;;KAhBD;AAoCT,SAAS,WAAW,EAAE,QAAQ,EAA0C;;IACtE,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc;IAElC,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;+BAAE,CAAC;YACR,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,QAAQ,EAAE;gBAC7C,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;gBACtD,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;gBACtD,MAAM,WAAW,OAAO,OAAO,CAAC,QAAQ;gBACxC,SAAS,OAAO,GAAG,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YACnE;QACF;;IAEA,qBACE,6LAAC,4JAAA,CAAA,QAAK;QAAC,OAAO;QAAG,mBAAmB;QAAK,gBAAgB;kBACvD,cAAA,6LAAC,6JAAA,CAAA,SAAM;YAAC,KAAK;YAAQ,UAAU;YAAU,MAAM;gBAAC;gBAAK;gBAAI;aAAG;sBAC1D,cAAA,6LAAC;gBACC,OAAM;gBACN,WAAW;gBACX,SAAS;gBACT,UAAS;gBACT,mBAAmB;;;;;;;;;;;;;;;;AAK7B;IAzBS;;QAGP,kNAAA,CAAA,WAAQ;;;MAHD;AA2BT,SAAS;;IACP,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAgB;IAC1C,MAAM,gBAAgB;IAEtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YACxB,MAAM,MAAM,IAAI,aAAa,gBAAgB;YAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,GAAG,KAAK,EAAG;gBAC7C,GAAG,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAQ,IAAI;gBAC7C,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI;gBAC7C,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI;YAC/C;YACA,OAAO;QACT;6CAAG;QAAC;KAAc;IAElB,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;oCAAE,CAAC;YACR,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;gBAE5D,+BAA+B;gBAC/B,MAAM,YAAY,aAAa,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK;gBACzE,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;oBAC5C,SAAS,CAAC,EAAE,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;gBAC1D;gBACA,aAAa,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,GAAG;YAClE;QACF;;IAEA,qBACE,6LAAC;QAAO,KAAK;;0BACX,6LAAC;0BACC,cAAA,6LAAC;oBACC,QAAO;oBACP,OAAO;oBACP,OAAO;oBACP,UAAU;;;;;;;;;;;0BAGd,6LAAC;gBACC,OAAM;gBACN,MAAM;gBACN,WAAW;gBACX,SAAS;gBACT,eAAe;;;;;;;;;;;;AAIvB;IA9CS;;QAcP,kNAAA,CAAA,WAAQ;;;MAdD;AAgDT,SAAS;;IACP,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc;IACpC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc;IAEpC,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;iCAAE,CAAC;YACR,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;gBACxD,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YAC1D;YACA,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;gBACxD,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YAC1D;QACF;;IAEA,qBACE;;0BACE,6LAAC;gBAAK,KAAK;gBAAU,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;;kCACxC,6LAAC;wBAAc,MAAM;4BAAC;4BAAG;4BAAM;4BAAG;yBAAI;;;;;;kCACtC,6LAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,UAAS;wBACT,mBAAmB;;;;;;;;;;;;0BAGvB,6LAAC;gBAAK,KAAK;gBAAU,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;;kCACxC,6LAAC;wBAAc,MAAM;4BAAC;4BAAG;4BAAM;4BAAG;yBAAI;;;;;;kCACtC,6LAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,UAAS;wBACT,mBAAmB;;;;;;;;;;;;;;AAK7B;IAvCS;;QAIP,kNAAA,CAAA,WAAQ;;;MAJD;AAyCT,SAAS;IACP,qBACE;;0BAEE,6LAAC;gBAAa,WAAW;;;;;;0BACzB,6LAAC;gBAAiB,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBAAE,WAAW;;;;;;0BACpD,6LAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAI;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BAC1D,6LAAC;gBAAW,UAAU;oBAAC;oBAAI,CAAC;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAM,OAAM;;;;;;0BAG3D,6LAAC;;;;;0BAGD,6LAAC;gBAAc,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;;;;;;0BACpC,6LAAC;gBAAc,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;;;;;;0BACnC,6LAAC;gBAAc,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;;;;;;0BAGnC,6LAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;;;;;;0BACjC,6LAAC;gBAAW,UAAU;oBAAC;oBAAG,CAAC;oBAAG,CAAC;iBAAE;;;;;;0BACjC,6LAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAG,CAAC;oBAAG,CAAC;iBAAE;;;;;;0BAGlC,6LAAC;;;;;;;AAGP;MA1BS;AA4BM,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sMAAA,CAAA,SAAM;YACL,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,KAAK;YAAG;YACvC,OAAO;gBAAE,YAAY;YAAc;YACnC,KAAK;gBAAC;gBAAG;aAAE;YACX,aAAa;gBAAE,KAAK;YAAI;YACxB,WAAU;YACV,IAAI;gBACF,WAAW;gBACX,OAAO;gBACP,iBAAiB;YACnB;sBAEA,cAAA,6LAAC;;;;;;;;;;;;;;;AAIT;MAnBwB", "debugId": null}}]}