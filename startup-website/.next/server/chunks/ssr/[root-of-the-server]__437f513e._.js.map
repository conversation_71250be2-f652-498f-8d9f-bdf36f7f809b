{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Medicello%20%26%20Co/Software%20Development/startup-website/src/components/Header.tsx"], "sourcesContent": ["export default function Header() {\n  return (\n    <nav className=\"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white bg-opacity-5 border border-white border-opacity-10 rounded-full pt-3 pr-4 pb-3 pl-4 shadow-xl backdrop-blur-md\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <svg className=\"w-6 h-6\" viewBox=\"0 0 24 24\" fill=\"none\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"white\" strokeWidth=\"2\"></circle>\n            <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"white\"></circle>\n          </svg>\n          <span className=\"ml-2 text-sm font-medium\">Minimal</span>\n        </div>\n        <div className=\"hidden md:flex items-center space-x-6 text-xs text-gray-300 ml-8\">\n          <a href=\"#\" className=\"hover:text-white transition-colors\">Work</a>\n          <a href=\"#\" className=\"hover:text-white transition-colors\">Studio</a>\n          <a href=\"#\" className=\"hover:text-white transition-colors\">Process</a>\n          <a href=\"#\" className=\"hover:text-white transition-colors\">Journal</a>\n          <a href=\"#\" className=\"hover:text-white transition-colors\">Contact</a>\n        </div>\n        <div className=\"flex items-center space-x-3 ml-8\">\n          <a href=\"#\" className=\"hidden md:inline-block text-xs font-medium hover:text-white transition-colors\">Login</a>\n          <a href=\"#\" className=\"hover:bg-gray-200 transition-colors text-xs font-medium text-black bg-white rounded-full pt-1.5 pr-3 pb-1.5 pl-3\">Start Project</a>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAU,SAAQ;4BAAY,MAAK;;8CAChD,8OAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,QAAO;oCAAQ,aAAY;;;;;;8CAC1D,8OAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAI,MAAK;;;;;;;;;;;;sCAErC,8OAAC;4BAAK,WAAU;sCAA2B;;;;;;;;;;;;8BAE7C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAqC;;;;;;sCAC3D,8OAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAqC;;;;;;sCAC3D,8OAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAqC;;;;;;sCAC3D,8OAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAqC;;;;;;sCAC3D,8OAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAqC;;;;;;;;;;;;8BAE7D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAgF;;;;;;sCACtG,8OAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAmH;;;;;;;;;;;;;;;;;;;;;;;AAKnJ", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Medicello%20%26%20Co/Software%20Development/startup-website/src/components/Footer.tsx"], "sourcesContent": ["export default function Footer() {\n  return (\n    <footer className=\"w-full py-6 px-4 border-t border-gray-200 dark:border-gray-800 mt-auto\">\n      <div className=\"max-w-6xl mx-auto text-center\">\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          © {new Date().getFullYear()} Your Startup. All rights reserved.\n        </p>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;;oBAAmC;oBAC3C,IAAI,OAAO,WAAW;oBAAG;;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Medicello%20%26%20Co/Software%20Development/startup-website/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Minimal Design Studio\",\n  description: \"Crafting digital experiences through minimalist design\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}\n      >\n        <Header />\n        <main className=\"flex-1\">\n          {children}\n        </main>\n        <Footer />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,uCAAuC,CAAC;;8BAE/F,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACb;;;;;;8BAEH,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Medicello%20%26%20Co/Software%20Development/startup-website/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}