{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Medicello%20%26%20Co/Software%20Development/startup-website/src/components/ThreeBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { Canvas } from '@react-three/fiber';\nimport { Float, Sphere, Torus } from '@react-three/drei';\nimport { useRef, useMemo, memo } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport * as THREE from 'three';\n\nconst FlowingStream = memo(({ position }: { position: [number, number, number] }) => {\n  const tubeRef = useRef<THREE.Mesh>(null);\n\n  const curve = useMemo(() => {\n    const points = [];\n    for (let i = 0; i <= 20; i++) {\n      const t = i / 20;\n      points.push(new THREE.Vector3(\n        Math.sin(t * Math.PI * 2) * 2,\n        (t - 0.5) * 4,\n        Math.cos(t * Math.PI * 2) * 2\n      ));\n    }\n    return new THREE.CatmullRomCurve3(points);\n  }, []);\n\n  useFrame((state) => {\n    if (tubeRef.current) {\n      tubeRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n    }\n  });\n\n  return (\n    <Float speed={0.5} rotationIntensity={0.2} floatIntensity={0.5}>\n      <mesh ref={tubeRef} position={position}>\n        <tubeGeometry args={[curve, 64, 0.02, 8, false]} />\n        <meshBasicMaterial\n          color=\"#60a5fa\"\n          transparent\n          opacity={0.6}\n        />\n      </mesh>\n    </Float>\n  );\n});\n\nFlowingStream.displayName = 'FlowingStream';\n\nconst ClarityOrb = memo(({ position }: { position: [number, number, number] }) => {\n  const orbRef = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (orbRef.current && orbRef.current.material) {\n      orbRef.current.rotation.x = state.clock.elapsedTime * 0.1;\n      orbRef.current.rotation.y = state.clock.elapsedTime * 0.15;\n      const material = orbRef.current.material as THREE.MeshStandardMaterial;\n      material.opacity = 0.3 + Math.sin(state.clock.elapsedTime * 2) * 0.1;\n    }\n  });\n\n  return (\n    <Float speed={1} rotationIntensity={0.5} floatIntensity={1}>\n      <Sphere ref={orbRef} position={position} args={[0.5, 32, 32]}>\n        <meshStandardMaterial\n          color=\"#a855f7\"\n          transparent\n          opacity={0.3}\n          emissive=\"#a855f7\"\n          emissiveIntensity={0.1}\n        />\n      </Sphere>\n    </Float>\n  );\n});\n\nClarityOrb.displayName = 'ClarityOrb';\n\nconst StreamParticles = memo(() => {\n  const particlesRef = useRef<THREE.Points>(null);\n  const particleCount = 150;\n\n  const positions = useMemo(() => {\n    const pos = new Float32Array(particleCount * 3);\n    for (let i = 0; i < particleCount * 3; i += 3) {\n      pos[i] = (Math.random() - 0.5) * 25;     // x\n      pos[i + 1] = (Math.random() - 0.5) * 20; // y\n      pos[i + 2] = (Math.random() - 0.5) * 25; // z\n    }\n    return pos;\n  }, [particleCount]);\n\n  useFrame((state) => {\n    if (particlesRef.current) {\n      // Only rotate the entire particle system, don't modify individual positions\n      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.01;\n      particlesRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.05;\n    }\n  });\n\n  return (\n    <points ref={particlesRef}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          count={particleCount}\n          array={positions}\n          itemSize={3}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        color=\"#60a5fa\"\n        size={0.02}\n        transparent\n        opacity={0.6}\n        sizeAttenuation\n      />\n    </points>\n  );\n});\n\nStreamParticles.displayName = 'StreamParticles';\n\nconst ClarityRings = memo(() => {\n  const ring1Ref = useRef<THREE.Mesh>(null);\n  const ring2Ref = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (ring1Ref.current) {\n      ring1Ref.current.rotation.x = state.clock.elapsedTime * 0.05;\n      ring1Ref.current.rotation.y = state.clock.elapsedTime * 0.03;\n    }\n    if (ring2Ref.current) {\n      ring2Ref.current.rotation.y = state.clock.elapsedTime * 0.04;\n      ring2Ref.current.rotation.z = state.clock.elapsedTime * 0.02;\n    }\n  });\n\n  return (\n    <>\n      <mesh ref={ring1Ref} position={[0, 0, -12]}>\n        <torusGeometry args={[6, 0.03, 8, 100]} />\n        <meshBasicMaterial\n          color=\"#60a5fa\"\n          transparent\n          opacity={0.15}\n          emissive=\"#60a5fa\"\n          emissiveIntensity={0.1}\n        />\n      </mesh>\n      <mesh ref={ring2Ref} position={[0, 0, -15]}>\n        <torusGeometry args={[8, 0.02, 8, 100]} />\n        <meshBasicMaterial\n          color=\"#a855f7\"\n          transparent\n          opacity={0.1}\n          emissive=\"#a855f7\"\n          emissiveIntensity={0.05}\n        />\n      </mesh>\n    </>\n  );\n});\n\nClarityRings.displayName = 'ClarityRings';\n\nconst Scene = memo(() => {\n  return (\n    <>\n      {/* Lighting setup for clarity theme */}\n      <ambientLight intensity={0.2} />\n      <directionalLight position={[10, 10, 5]} intensity={0.3} />\n      <pointLight position={[-10, 5, -5]} intensity={0.2} color=\"#60a5fa\" />\n      <pointLight position={[10, -5, -5]} intensity={0.15} color=\"#a855f7\" />\n\n      {/* Background rings */}\n      <ClarityRings />\n\n      {/* Flowing streams */}\n      <FlowingStream position={[-3, 0, -5]} />\n      <FlowingStream position={[3, 0, -7]} />\n      <FlowingStream position={[0, 2, -6]} />\n\n      {/* Clarity orbs */}\n      <ClarityOrb position={[-2, 3, -3]} />\n      <ClarityOrb position={[4, -2, -4]} />\n      <ClarityOrb position={[-1, -1, -2]} />\n\n      {/* Stream particles */}\n      <StreamParticles />\n    </>\n  );\n});\n\nScene.displayName = 'Scene';\n\nexport default function ThreeBackground() {\n  return (\n    <div className=\"fixed inset-0 z-0\">\n      <Canvas\n        camera={{ position: [0, 0, 5], fov: 75 }}\n        style={{ background: 'transparent' }}\n        dpr={[1, 2]}\n        performance={{ min: 0.5 }}\n        frameloop=\"always\"\n        gl={{\n          antialias: true,\n          alpha: true,\n          powerPreference: \"high-performance\"\n        }}\n      >\n        <Scene />\n      </Canvas>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,QAAQ,EAA0C;IAC9E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAEnC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,SAAS,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;YAC5B,MAAM,IAAI,IAAI;YACd,OAAO,IAAI,CAAC,IAAI,+IAAA,CAAA,UAAa,CAC3B,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,GAAG,KAAK,GAC5B,CAAC,IAAI,GAAG,IAAI,GACZ,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,GAAG,KAAK;QAEhC;QACA,OAAO,IAAI,+IAAA,CAAA,mBAAsB,CAAC;IACpC,GAAG,EAAE;IAEL,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QACzD;IACF;IAEA,qBACE,8OAAC,yJAAA,CAAA,QAAK;QAAC,OAAO;QAAK,mBAAmB;QAAK,gBAAgB;kBACzD,cAAA,8OAAC;YAAK,KAAK;YAAS,UAAU;;8BAC5B,8OAAC;oBAAa,MAAM;wBAAC;wBAAO;wBAAI;wBAAM;wBAAG;qBAAM;;;;;;8BAC/C,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAKnB;AAEA,cAAc,WAAW,GAAG;AAE5B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,QAAQ,EAA0C;IAC3E,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAElC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,QAAQ,EAAE;YAC7C,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACtD,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACtD,MAAM,WAAW,OAAO,OAAO,CAAC,QAAQ;YACxC,SAAS,OAAO,GAAG,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;QACnE;IACF;IAEA,qBACE,8OAAC,yJAAA,CAAA,QAAK;QAAC,OAAO;QAAG,mBAAmB;QAAK,gBAAgB;kBACvD,cAAA,8OAAC,0JAAA,CAAA,SAAM;YAAC,KAAK;YAAQ,UAAU;YAAU,MAAM;gBAAC;gBAAK;gBAAI;aAAG;sBAC1D,cAAA,8OAAC;gBACC,OAAM;gBACN,WAAW;gBACX,SAAS;gBACT,UAAS;gBACT,mBAAmB;;;;;;;;;;;;;;;;AAK7B;AAEA,WAAW,WAAW,GAAG;AAEzB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;IAC3B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IAC1C,MAAM,gBAAgB;IAEtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,MAAM,IAAI,aAAa,gBAAgB;QAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,GAAG,KAAK,EAAG;YAC7C,GAAG,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAQ,IAAI;YAC7C,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI;YAC7C,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI;QAC/C;QACA,OAAO;IACT,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,aAAa,OAAO,EAAE;YACxB,4EAA4E;YAC5E,aAAa,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YAC5D,aAAa,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QAC9E;IACF;IAEA,qBACE,8OAAC;QAAO,KAAK;;0BACX,8OAAC;0BACC,cAAA,8OAAC;oBACC,QAAO;oBACP,OAAO;oBACP,OAAO;oBACP,UAAU;;;;;;;;;;;0BAGd,8OAAC;gBACC,OAAM;gBACN,MAAM;gBACN,WAAW;gBACX,SAAS;gBACT,eAAe;;;;;;;;;;;;AAIvB;AAEA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;IACxB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACpC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAEpC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACxD,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC1D;QACA,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACxD,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC1D;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAK,KAAK;gBAAU,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;;kCACxC,8OAAC;wBAAc,MAAM;4BAAC;4BAAG;4BAAM;4BAAG;yBAAI;;;;;;kCACtC,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,UAAS;wBACT,mBAAmB;;;;;;;;;;;;0BAGvB,8OAAC;gBAAK,KAAK;gBAAU,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;;kCACxC,8OAAC;wBAAc,MAAM;4BAAC;4BAAG;4BAAM;4BAAG;yBAAI;;;;;;kCACtC,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,UAAS;wBACT,mBAAmB;;;;;;;;;;;;;;AAK7B;AAEA,aAAa,WAAW,GAAG;AAE3B,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;IACjB,qBACE;;0BAEE,8OAAC;gBAAa,WAAW;;;;;;0BACzB,8OAAC;gBAAiB,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBAAE,WAAW;;;;;;0BACpD,8OAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAI;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BAC1D,8OAAC;gBAAW,UAAU;oBAAC;oBAAI,CAAC;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAM,OAAM;;;;;;0BAG3D,8OAAC;;;;;0BAGD,8OAAC;gBAAc,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;;;;;;0BACpC,8OAAC;gBAAc,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;;;;;;0BACnC,8OAAC;gBAAc,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;;;;;;0BAGnC,8OAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;;;;;;0BACjC,8OAAC;gBAAW,UAAU;oBAAC;oBAAG,CAAC;oBAAG,CAAC;iBAAE;;;;;;0BACjC,8OAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAG,CAAC;oBAAG,CAAC;iBAAE;;;;;;0BAGlC,8OAAC;;;;;;;AAGP;AAEA,MAAM,WAAW,GAAG;AAEL,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,mMAAA,CAAA,SAAM;YACL,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,KAAK;YAAG;YACvC,OAAO;gBAAE,YAAY;YAAc;YACnC,KAAK;gBAAC;gBAAG;aAAE;YACX,aAAa;gBAAE,KAAK;YAAI;YACxB,WAAU;YACV,IAAI;gBACF,WAAW;gBACX,OAAO;gBACP,iBAAiB;YACnB;sBAEA,cAAA,8OAAC;;;;;;;;;;;;;;;AAIT", "debugId": null}}]}