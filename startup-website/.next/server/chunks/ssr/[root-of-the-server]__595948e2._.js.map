{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Medicello%20%26%20Co/Software%20Development/startup-website/src/components/ThreeBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { Canvas } from '@react-three/fiber';\nimport { Float, Sphere, Box, Torus } from '@react-three/drei';\nimport { useRef, useMemo } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport * as THREE from 'three';\n\nfunction FloatingGeometry({ position, geometry }: { position: [number, number, number], geometry: 'sphere' | 'box' | 'torus' }) {\n  const meshRef = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.x = state.clock.elapsedTime * 0.2;\n      meshRef.current.rotation.y = state.clock.elapsedTime * 0.3;\n    }\n  });\n\n  const GeometryComponent = geometry === 'sphere' ? Sphere : geometry === 'box' ? Box : Torus;\n\n  return (\n    <Float speed={1.5} rotationIntensity={1} floatIntensity={2}>\n      <GeometryComponent ref={meshRef} position={position} args={geometry === 'torus' ? [0.5, 0.2, 8, 16] : [0.8]}>\n        <meshStandardMaterial\n          color=\"#ffffff\"\n          transparent\n          opacity={0.1}\n          wireframe\n        />\n      </GeometryComponent>\n    </Float>\n  );\n}\n\nfunction Particles() {\n  const particlesRef = useRef<THREE.Points>(null);\n  const particleCount = 200;\n\n  const positions = useMemo(() => {\n    const pos = new Float32Array(particleCount * 3);\n    for (let i = 0; i < particleCount * 3; i++) {\n      pos[i] = (Math.random() - 0.5) * 30;\n    }\n    return pos;\n  }, [particleCount]);\n\n  useFrame((state) => {\n    if (particlesRef.current) {\n      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.02;\n      particlesRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.1;\n    }\n  });\n\n  return (\n    <points ref={particlesRef}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          count={particleCount}\n          array={positions}\n          itemSize={3}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        color=\"#ffffff\"\n        size={0.015}\n        transparent\n        opacity={0.4}\n        sizeAttenuation\n      />\n    </points>\n  );\n}\n\nfunction MovingRings() {\n  const ring1Ref = useRef<THREE.Mesh>(null);\n  const ring2Ref = useRef<THREE.Mesh>(null);\n  const ring3Ref = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (ring1Ref.current) {\n      ring1Ref.current.rotation.x = state.clock.elapsedTime * 0.1;\n      ring1Ref.current.rotation.z = state.clock.elapsedTime * 0.15;\n    }\n    if (ring2Ref.current) {\n      ring2Ref.current.rotation.y = state.clock.elapsedTime * 0.08;\n      ring2Ref.current.rotation.x = state.clock.elapsedTime * 0.12;\n    }\n    if (ring3Ref.current) {\n      ring3Ref.current.rotation.z = state.clock.elapsedTime * 0.06;\n      ring3Ref.current.rotation.y = state.clock.elapsedTime * 0.1;\n    }\n  });\n\n  return (\n    <>\n      <mesh ref={ring1Ref} position={[0, 0, -8]}>\n        <torusGeometry args={[3, 0.02, 8, 100]} />\n        <meshBasicMaterial color=\"#ffffff\" transparent opacity={0.1} />\n      </mesh>\n      <mesh ref={ring2Ref} position={[0, 0, -6]}>\n        <torusGeometry args={[2, 0.015, 8, 100]} />\n        <meshBasicMaterial color=\"#ffffff\" transparent opacity={0.08} />\n      </mesh>\n      <mesh ref={ring3Ref} position={[0, 0, -10]}>\n        <torusGeometry args={[4, 0.025, 8, 100]} />\n        <meshBasicMaterial color=\"#ffffff\" transparent opacity={0.06} />\n      </mesh>\n    </>\n  );\n}\n\nfunction Scene() {\n  return (\n    <>\n      {/* Ambient lighting */}\n      <ambientLight intensity={0.15} />\n      <directionalLight position={[10, 10, 5]} intensity={0.2} />\n      <pointLight position={[-10, -10, -10]} intensity={0.1} color=\"#4f46e5\" />\n\n      {/* Moving rings in background */}\n      <MovingRings />\n\n      {/* Floating geometric shapes */}\n      <FloatingGeometry position={[-4, 2, -2]} geometry=\"sphere\" />\n      <FloatingGeometry position={[4, -1, -3]} geometry=\"box\" />\n      <FloatingGeometry position={[0, 3, -4]} geometry=\"torus\" />\n      <FloatingGeometry position={[-2, -2, -1]} geometry=\"sphere\" />\n      <FloatingGeometry position={[3, 1, -2]} geometry=\"torus\" />\n      <FloatingGeometry position={[-1, 0, -5]} geometry=\"box\" />\n\n      {/* Particles */}\n      <Particles />\n    </>\n  );\n}\n\nexport default function ThreeBackground() {\n  return (\n    <div className=\"fixed inset-0 z-0\">\n      <Canvas\n        camera={{ position: [0, 0, 5], fov: 75 }}\n        style={{ background: 'transparent' }}\n        dpr={[1, 2]}\n        performance={{ min: 0.5 }}\n        frameloop=\"demand\"\n        gl={{\n          antialias: false,\n          alpha: true,\n          powerPreference: \"high-performance\"\n        }}\n      >\n        <Scene />\n      </Canvas>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAQA,SAAS,iBAAiB,EAAE,QAAQ,EAAE,QAAQ,EAAgF;IAC5H,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAEnC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACvD,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QACzD;IACF;IAEA,MAAM,oBAAoB,aAAa,WAAW,0JAAA,CAAA,SAAM,GAAG,aAAa,QAAQ,0JAAA,CAAA,MAAG,GAAG,0JAAA,CAAA,QAAK;IAE3F,qBACE,8OAAC,yJAAA,CAAA,QAAK;QAAC,OAAO;QAAK,mBAAmB;QAAG,gBAAgB;kBACvD,cAAA,8OAAC;YAAkB,KAAK;YAAS,UAAU;YAAU,MAAM,aAAa,UAAU;gBAAC;gBAAK;gBAAK;gBAAG;aAAG,GAAG;gBAAC;aAAI;sBACzG,cAAA,8OAAC;gBACC,OAAM;gBACN,WAAW;gBACX,SAAS;gBACT,SAAS;;;;;;;;;;;;;;;;AAKnB;AAEA,SAAS;IACP,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IAC1C,MAAM,gBAAgB;IAEtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,MAAM,IAAI,aAAa,gBAAgB;QAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,GAAG,IAAK;YAC1C,GAAG,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QACnC;QACA,OAAO;IACT,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YAC5D,aAAa,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QAC9E;IACF;IAEA,qBACE,8OAAC;QAAO,KAAK;;0BACX,8OAAC;0BACC,cAAA,8OAAC;oBACC,QAAO;oBACP,OAAO;oBACP,OAAO;oBACP,UAAU;;;;;;;;;;;0BAGd,8OAAC;gBACC,OAAM;gBACN,MAAM;gBACN,WAAW;gBACX,SAAS;gBACT,eAAe;;;;;;;;;;;;AAIvB;AAEA,SAAS;IACP,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACpC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACpC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAEpC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACxD,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC1D;QACA,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACxD,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC1D;QACA,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACxD,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC1D;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAK,KAAK;gBAAU,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;;kCACvC,8OAAC;wBAAc,MAAM;4BAAC;4BAAG;4BAAM;4BAAG;yBAAI;;;;;;kCACtC,8OAAC;wBAAkB,OAAM;wBAAU,WAAW;wBAAC,SAAS;;;;;;;;;;;;0BAE1D,8OAAC;gBAAK,KAAK;gBAAU,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;;kCACvC,8OAAC;wBAAc,MAAM;4BAAC;4BAAG;4BAAO;4BAAG;yBAAI;;;;;;kCACvC,8OAAC;wBAAkB,OAAM;wBAAU,WAAW;wBAAC,SAAS;;;;;;;;;;;;0BAE1D,8OAAC;gBAAK,KAAK;gBAAU,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;;kCACxC,8OAAC;wBAAc,MAAM;4BAAC;4BAAG;4BAAO;4BAAG;yBAAI;;;;;;kCACvC,8OAAC;wBAAkB,OAAM;wBAAU,WAAW;wBAAC,SAAS;;;;;;;;;;;;;;AAIhE;AAEA,SAAS;IACP,qBACE;;0BAEE,8OAAC;gBAAa,WAAW;;;;;;0BACzB,8OAAC;gBAAiB,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBAAE,WAAW;;;;;;0BACpD,8OAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAI,CAAC;oBAAI,CAAC;iBAAG;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BAG7D,8OAAC;;;;;0BAGD,8OAAC;gBAAiB,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,UAAS;;;;;;0BAClD,8OAAC;gBAAiB,UAAU;oBAAC;oBAAG,CAAC;oBAAG,CAAC;iBAAE;gBAAE,UAAS;;;;;;0BAClD,8OAAC;gBAAiB,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,UAAS;;;;;;0BACjD,8OAAC;gBAAiB,UAAU;oBAAC,CAAC;oBAAG,CAAC;oBAAG,CAAC;iBAAE;gBAAE,UAAS;;;;;;0BACnD,8OAAC;gBAAiB,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,UAAS;;;;;;0BACjD,8OAAC;gBAAiB,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,UAAS;;;;;;0BAGlD,8OAAC;;;;;;;AAGP;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,mMAAA,CAAA,SAAM;YACL,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,KAAK;YAAG;YACvC,OAAO;gBAAE,YAAY;YAAc;YACnC,KAAK;gBAAC;gBAAG;aAAE;YACX,aAAa;gBAAE,KAAK;YAAI;YACxB,WAAU;YACV,IAAI;gBACF,WAAW;gBACX,OAAO;gBACP,iBAAiB;YACnB;sBAEA,cAAA,8OAAC;;;;;;;;;;;;;;;AAIT", "debugId": null}}]}